"use client";

import React from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { GlobalSearch } from "./global-search";
import { UserMenu } from "./user-menu";
import { NavbarProps } from "@/types/navbar";
import { cn } from "@/lib/utils";

export function Navbar({ user }: NavbarProps) {
  const { data: session } = useSession();
  const router = useRouter();

  // Use session user if no user prop provided
  const currentUser = user || session?.user;

  // Handle global search
  const handleGlobalSearch = (query: string) => {
    // For now, redirect to garanties page with search query
    // This can be enhanced later with a dedicated search page
    const searchParams = new URLSearchParams({ search: query });
    router.push(`/garanties?${searchParams.toString()}`);
  };

  // Don't render navbar if no user
  if (!currentUser) {
    return null;
  }

  return (
    <header
      className="fixed top-0 left-0 right-0 z-50 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b border-border"
      role="banner"
      aria-label="Navigation principale"
    >
      <div className="flex h-16 items-center justify-between px-4 md:px-6">
        {/* Left section: Logo/Brand */}
        <div className="flex items-center gap-4 min-w-0">
          <div className="hidden sm:block">
            <h1 className="text-lg font-semibold text-primary">
              GesGar
            </h1>
          </div>
          {/* Mobile: Show just "G" on very small screens */}
          <div className="sm:hidden">
            <h1 className="text-lg font-bold text-primary w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
              G
            </h1>
          </div>
        </div>

        {/* Center section: Global Search */}
        <div className="flex-1 flex justify-center max-w-2xl mx-4">
          <GlobalSearch
            onSearch={handleGlobalSearch}
            placeholder="Rechercher garanties, projets, partenaires..."
            className="w-full"
          />
        </div>

        {/* Right section: User Menu */}
        <div className="flex items-center">
          <UserMenu user={currentUser} />
        </div>
      </div>
    </header>
  );
}

// Export a wrapper component that handles session automatically
export function NavbarWithSession() {
  const { data: session } = useSession();

  return <Navbar user={session?.user} />;
}
